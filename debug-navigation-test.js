// Debug script to test navigation functionality
console.log('=== NAVIGATION DEBUG TEST ===');

// Test immediately when script loads
console.log('Script loaded at:', new Date().toISOString());

// Test 1: Check if elements exist immediately
function testElementsExist() {
    console.log('\n--- Testing if elements exist ---');
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    const contentSections = document.querySelectorAll('.content-section');

    console.log('Sidebar items found:', sidebarItems.length);
    console.log('Content sections found:', contentSections.length);

    if (sidebarItems.length === 0) {
        console.log('❌ No sidebar items found - DOM might not be ready');
        return false;
    }

    sidebarItems.forEach((item, index) => {
        console.log(`  Item ${index}: "${item.textContent.trim()}" data-section="${item.dataset.section}"`);
    });

    contentSections.forEach((section, index) => {
        console.log(`  Section ${index}: id="${section.id}" active="${section.classList.contains('active')}"`);
    });

    return true;
}

// Test 2: Check if functions exist
function testFunctionsExist() {
    console.log('\n--- Testing if functions exist ---');
    console.log('switchToSection function exists:', typeof window.switchToSection === 'function');
    console.log('initializeNavigation function exists:', typeof window.initializeNavigation === 'function');
}

// Test 3: Manually add event listeners to test
function addTestEventListeners() {
    console.log('\n--- Adding test event listeners ---');
    const sidebarItems = document.querySelectorAll('.sidebar-item');

    sidebarItems.forEach((item, index) => {
        item.addEventListener('click', function(e) {
            console.log(`TEST CLICK: Sidebar item ${index} clicked:`, this.textContent.trim());
            console.log('Data section:', this.dataset.section);
            e.preventDefault();
            e.stopPropagation();
        });
    });

    console.log('Test event listeners added to', sidebarItems.length, 'items');
}

// Test 4: Try to manually switch sections
function testManualSwitch() {
    console.log('\n--- Testing manual section switch ---');

    // Hide all sections
    const allSections = document.querySelectorAll('.content-section');
    allSections.forEach(section => {
        section.classList.remove('active');
        console.log('Removed active from:', section.id);
    });

    // Show explore section
    const exploreSection = document.getElementById('explore-section');
    if (exploreSection) {
        exploreSection.classList.add('active');
        console.log('Added active to explore-section');

        // Update sidebar
        const sidebarItems = document.querySelectorAll('.sidebar-item');
        sidebarItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === 'explore') {
                item.classList.add('active');
                console.log('Updated sidebar active state');
            }
        });
    } else {
        console.log('❌ explore-section not found');
    }
}

// Run tests when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('\n=== DOM CONTENT LOADED ===');

    setTimeout(() => {
        testElementsExist();
        testFunctionsExist();
        addTestEventListeners();

        // Test manual switch after 2 seconds
        setTimeout(() => {
            testManualSwitch();
        }, 2000);
    }, 500);
});

// Also run tests immediately in case DOM is already loaded
if (document.readyState === 'loading') {
    console.log('DOM is still loading...');
} else {
    console.log('DOM already loaded, running tests...');
    setTimeout(() => {
        testElementsExist();
        testFunctionsExist();
        addTestEventListeners();
    }, 100);
}
