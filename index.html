<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 100vw;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: transparent;
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        .nav-btn.primary {
            background: #8B4513;
            color: white;
        }

        .nav-btn.primary:hover {
            background: #7a3c0f;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 30px;
            align-items: start;
            min-height: calc(100vh - 140px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 8px;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        .sidebar-item:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: rgba(139, 69, 19, 0.15);
            color: #8B4513;
            font-weight: 600;
        }

        .sidebar-item::before {
            content: "📱";
            margin-right: 12px;
            font-size: 16px;
        }

        .sidebar-item:nth-child(2)::before { content: "🏠"; }
        .sidebar-item:nth-child(3)::before { content: "🔍"; }
        .sidebar-item:nth-child(4)::before { content: "💬"; }
        .sidebar-item:nth-child(5)::before { content: "👤"; }

        /* Feed Section */
        .feed-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .feed-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
        }

        .create-post {
            background: rgba(201, 168, 118, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px dashed rgba(201, 168, 118, 0.3);
            text-align: center;
        }

        .create-post-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .create-post h3 {
            color: #c9a876;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .create-post p {
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .create-btn {
            background: linear-gradient(45deg, #c9a876, #f4e4bc);
            color: #1a1a1a;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .create-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .load-more {
            background: rgba(139, 69, 19, 0.05);
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            width: 100%;
        }

        .load-more:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        /* Right Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .trending-item {
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trending-item:hover {
            background: rgba(201, 168, 118, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 0 -15px;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-topic {
            font-weight: 600;
            color: #c9a876;
            margin-bottom: 5px;
        }

        .trending-count {
            font-size: 12px;
            color: #999;
        }

        /* Mobile Bottom Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .mobile-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 50px;
            flex: 1;
            max-width: 80px;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            touch-action: manipulation;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
            transform: translateY(-2px);
        }

        .mobile-nav-item .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .mobile-nav-item .label {
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">Naroop</div>
            <div class="nav-buttons">
                <button class="nav-btn" onclick="showSignIn()">Sign In</button>
                <button class="nav-btn primary" onclick="showSignUp()">Sign Up</button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Sidebar -->
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="sidebar-item active" data-section="feed">Feed</div>
                <div class="sidebar-item" data-section="explore">Explore</div>
                <div class="sidebar-item" data-section="messages">Messages</div>
                <div class="sidebar-item" data-section="profile">Profile</div>
            </aside>

            <!-- Feed Section -->
            <section class="feed-section content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>

                <div class="create-post">
                    <div class="create-post-icon">✨</div>
                    <h3>Share Your Story</h3>
                    <p>What positive experience would you like to share with the community today?</p>
                    <button class="create-btn">Create Post</button>
                </div>

                <div id="posts-container">
                    <!-- Posts will be loaded here -->
                </div>

                <button class="load-more">Load More Posts</button>
            </section>

            <!-- Right Sidebar -->
            <aside class="trending">
                <h3>Trending Topics</h3>
                <div class="trending-item">
                    <div class="trending-topic">#BlackExcellence</div>
                    <div class="trending-count">2.1M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#CommunityLove</div>
                    <div class="trending-count">980K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Inspiration</div>
                    <div class="trending-count">1.5M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#BlackJoy</div>
                    <div class="trending-count">750K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Success</div>
                    <div class="trending-count">1.2M posts</div>
                </div>
            </aside>
        </main>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-nav-items">
                <div class="mobile-nav-item active" data-section="feed">
                    <div class="icon">🏠</div>
                    <div class="label">Home</div>
                </div>
                <div class="mobile-nav-item" data-section="explore">
                    <div class="icon">🔍</div>
                    <div class="label">Explore</div>
                </div>
                <div class="mobile-nav-item" data-section="messages">
                    <div class="icon">💬</div>
                    <div class="label">Messages</div>
                </div>
                <div class="mobile-nav-item" data-section="profile">
                    <div class="icon">👤</div>
                    <div class="label">Profile</div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Mobile Responsive Styles -->
    <style>
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding-bottom: 80px;
            }

            .sidebar,
            .trending {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .feed-section {
                padding: 20px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
            }

            .create-post h3 {
                font-size: 18px;
            }

            .create-post p {
                font-size: 13px;
            }

            .create-btn {
                padding: 12px 24px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .header {
                padding: 12px 15px;
                border-radius: 15px;
            }

            .logo {
                font-size: 20px;
            }

            .nav-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .feed-section {
                padding: 15px;
                border-radius: 15px;
            }

            .create-post {
                padding: 15px;
                border-radius: 15px;
            }

            .create-post-icon {
                font-size: 36px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .mobile-nav-item .icon {
                font-size: 18px;
            }

            .mobile-nav-item .label {
                font-size: 10px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script>
        // Authentication functions (placeholders)
        function showSignIn() {
            alert('Sign In functionality will be implemented with Firebase Authentication');
        }

        function showSignUp() {
            alert('Sign Up functionality will be implemented with Firebase Authentication');
        }

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Desktop sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            function setActiveNavigation(section) {
                // Update desktop sidebar
                sidebarItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === section) {
                        item.classList.add('active');
                    }
                });

                // Update mobile navigation
                mobileNavItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === section) {
                        item.classList.add('active');
                    }
                });

                // Here you would typically show/hide different content sections
                console.log('Navigating to:', section);
            }

            // Add click handlers for desktop sidebar
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    const section = this.dataset.section;
                    setActiveNavigation(section);
                });
            });

            // Add click handlers for mobile navigation
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    const section = this.dataset.section;
                    setActiveNavigation(section);
                });
            });

            // Refresh button functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    console.log('Refreshing feed...');
                    // Add refresh animation
                    this.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 500);
                });
            }

            // Create post button functionality
            const createBtn = document.querySelector('.create-btn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    alert('Create Post functionality will be implemented');
                });
            }

            // Load more button functionality
            const loadMoreBtn = document.querySelector('.load-more');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', function() {
                    console.log('Loading more posts...');
                    this.textContent = 'Loading...';
                    setTimeout(() => {
                        this.textContent = 'Load More Posts';
                    }, 1000);
                });
            }

            // Trending items functionality
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const topic = this.querySelector('.trending-topic').textContent;
                    console.log('Clicked trending topic:', topic);
                });
            });
        });

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add loading state management
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.3s ease-in-out';
        });

        // Add touch feedback for mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', function() {}, {passive: true});
        }
    </script>

    <!-- Include external JavaScript files -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>

</body>
</html>
