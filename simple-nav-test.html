<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Navigation Test</title>
    <style>
        .sidebar-item {
            padding: 10px;
            margin: 5px;
            background: #f0f0f0;
            cursor: pointer;
            border: 1px solid #ccc;
        }
        .sidebar-item:hover {
            background: #e0e0e0;
        }
        .sidebar-item.active {
            background: #007bff;
            color: white;
        }
        .content-section {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            margin: 10px;
        }
        .content-section.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Simple Navigation Test</h1>
    
    <div class="sidebar">
        <div class="sidebar-item active" data-section="feed">Feed</div>
        <div class="sidebar-item" data-section="explore">Explore</div>
        <div class="sidebar-item" data-section="messages">Messages</div>
        <div class="sidebar-item" data-section="profile">Profile</div>
        <div class="sidebar-item" data-section="settings">Settings</div>
    </div>
    
    <div class="content">
        <div class="content-section active" id="feed-section">
            <h2>Feed Section</h2>
            <p>This is the feed content.</p>
        </div>
        <div class="content-section" id="explore-section">
            <h2>Explore Section</h2>
            <p>This is the explore content.</p>
        </div>
        <div class="content-section" id="messages-section">
            <h2>Messages Section</h2>
            <p>This is the messages content.</p>
        </div>
        <div class="content-section" id="profile-section">
            <h2>Profile Section</h2>
            <p>This is the profile content.</p>
        </div>
        <div class="content-section" id="settings-section">
            <h2>Settings Section</h2>
            <p>This is the settings content.</p>
        </div>
    </div>

    <script>
        console.log('Simple navigation test script loaded');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded for simple test');
            
            function switchToSection(sectionName) {
                console.log('switchToSection called with:', sectionName);
                
                // Hide all content sections
                const allSections = document.querySelectorAll('.content-section');
                console.log('Found content sections:', allSections.length);
                allSections.forEach(section => {
                    section.classList.remove('active');
                });
                
                // Show the selected section
                const targetSection = document.getElementById(`${sectionName}-section`);
                console.log('Target section:', targetSection);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log('Added active class to:', targetSection.id);
                } else {
                    console.error('Target section not found:', `${sectionName}-section`);
                }
                
                // Update sidebar navigation active state
                const sidebarItems = document.querySelectorAll('.sidebar-item');
                sidebarItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === sectionName) {
                        item.classList.add('active');
                    }
                });
                
                console.log(`Switched to ${sectionName} section`);
            }
            
            function initializeNavigation() {
                console.log('Initializing navigation...');
                
                const sidebarItems = document.querySelectorAll('.sidebar-item');
                console.log('Found sidebar items:', sidebarItems.length);
                sidebarItems.forEach(item => {
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Sidebar item clicked:', this.textContent.trim());
                        const sectionName = this.dataset.section;
                        console.log('Section name:', sectionName);
                        if (sectionName) {
                            switchToSection(sectionName);
                        }
                    });
                });
                
                console.log('Navigation initialized successfully');
            }
            
            // Initialize navigation
            initializeNavigation();
        });
    </script>
</body>
</html>
