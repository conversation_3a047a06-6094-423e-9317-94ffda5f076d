// Debug script to check navigation functionality
console.log('=== NAVIGATION DEBUG SCRIPT ===');

// Check if DOM elements exist
console.log('Checking DOM elements...');
const sidebarItems = document.querySelectorAll('.sidebar-item');
const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
const contentSections = document.querySelectorAll('.content-section');

console.log('Sidebar items found:', sidebarItems.length);
console.log('Mobile nav items found:', mobileNavItems.length);
console.log('Content sections found:', contentSections.length);

// List all sidebar items
console.log('\nSidebar items:');
sidebarItems.forEach((item, index) => {
    console.log(`${index + 1}. ${item.textContent} - data-section: ${item.dataset.section}`);
});

// List all content sections
console.log('\nContent sections:');
contentSections.forEach((section, index) => {
    console.log(`${index + 1}. ${section.id} - has active class: ${section.classList.contains('active')}`);
});

// Test navigation function
console.log('\n=== TESTING NAVIGATION ===');
function testNavigation() {
    console.log('Testing navigation to explore section...');
    
    // Find the explore sidebar item
    const exploreItem = document.querySelector('.sidebar-item[data-section="explore"]');
    if (exploreItem) {
        console.log('Found explore item, clicking...');
        exploreItem.click();
        
        setTimeout(() => {
            const exploreSection = document.getElementById('explore-section');
            if (exploreSection && exploreSection.classList.contains('active')) {
                console.log('✅ Navigation working! Explore section is now active');
            } else {
                console.log('❌ Navigation failed! Explore section is not active');
            }
        }, 100);
    } else {
        console.log('❌ Explore item not found');
    }
}

// Run test after a short delay
setTimeout(testNavigation, 1000);
